import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import threading
from datetime import datetime
import logging
import traceback
import os

# Configuração do logging
logging.basicConfig(filename='busca_nome_cpf.log', level=logging.DEBUG,
                    format='%(asctime)s - %(levelname)s - %(message)s')

# Importar SeleniumBase
try:
    from seleniumbase import SB
    SELENIUMBASE_AVAILABLE = True
    logging.info("SeleniumBase disponível - usando CDP Mode para máxima proteção anti-detecção")
except ImportError:
    SELENIUMBASE_AVAILABLE = False
    logging.error("SeleniumBase não disponível - instale com: pip install seleniumbase")


class Application(tk.Frame):
    def __init__(self, master=None):
        super().__init__(master)
        self.master = master
        self.master.title("Busca Nome e CPF - CDP Mode Anti-Detecção")
        self.master.geometry("800x700")
        self.master.configure(bg="#2c3e50")
        self.excel_dir = None
        self.porcentagem_concluida = 0
        self.processo_num = "Inicializando..."
        self.last_result = "Buscando..."
        self.salvando = ""
        self.processos_nao_encontrados = 0
        self.total_processos = 0
        self.not_found_logs = []
        self.create_widgets()

    def create_widgets(self):
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TFrame', background="#2c3e50")
        style.configure('TButton', font=('Arial', 10, 'bold'), borderwidth=1,
                        background="#3498db", foreground="white")
        style.configure('TLabel', font=('Arial', 11),
                        background="#2c3e50", foreground="white")
        style.configure('TEntry', font=('Arial', 10),
                        fieldbackground="#34495e", foreground="white")

        main_frame = ttk.Frame(
            self.master, padding="30 30 30 30", style='TFrame')
        main_frame.grid(row=0, column=0, sticky=(
            tk.W, tk.E, tk.N, tk.S))
        main_frame.columnconfigure(0, weight=1)
        self.master.columnconfigure(0, weight=1)
        self.master.rowconfigure(0, weight=1)

        ttk.Label(main_frame, text="Busca Nome e CPF - CDP Mode", font=(
            'Arial', 16, 'bold')).grid(column=0, row=0, columnspan=2, pady=20)

        ttk.Label(main_frame, text="Usuário:").grid(
            column=0, row=1, sticky=tk.W, pady=5)
        self.entrada_usuario = ttk.Entry(main_frame)
        self.entrada_usuario.grid(
            column=0, row=2, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(main_frame, text="Senha:").grid(
            column=0, row=3, sticky=tk.W, pady=5)
        self.entrada_senha = ttk.Entry(main_frame, show="*")
        self.entrada_senha.grid(
            column=0, row=4, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(main_frame, text="Selecione a planilha Excel (primeira coluna = números de processo):").grid(
            column=0, row=5, sticky=tk.W, pady=10)
        self.excel_dir_entry = ttk.Entry(main_frame, width=50)
        self.excel_dir_entry.grid(
            column=0, row=6, sticky=(tk.W, tk.E), pady=5)
        ttk.Button(main_frame, text="Selecionar Arquivo",
                   command=self.select_file).grid(column=1, row=6, sticky=tk.W, padx=5, pady=5)

        ttk.Button(main_frame, text="Iniciar Busca", command=self.start_progress).grid(
            column=0, row=7, sticky=(tk.W, tk.E), pady=20, columnspan=2)

    def select_file(self):
        file_path = filedialog.askopenfilename(
            filetypes=[("Excel files", "*.xlsx *.xls")])
        self.excel_dir_entry.delete(0, tk.END)
        self.excel_dir_entry.insert(0, file_path)

    def start_progress(self):
        if not SELENIUMBASE_AVAILABLE:
            messagebox.showerror(
                "Erro", "SeleniumBase não está instalado. Execute: pip install seleniumbase")
            return
            
        self.excel_dir = self.excel_dir_entry.get()
        usuario = self.entrada_usuario.get()
        senha = self.entrada_senha.get()

        if not self.excel_dir:
            messagebox.showerror(
                "Erro", "Por favor, selecione um arquivo Excel.")
            return
        if not usuario or not senha:
            messagebox.showerror(
                "Erro", "Por favor, preencha o usuário e senha.")
            return

        t = threading.Thread(target=self.processar_planilha,
                             args=(usuario, senha))
        t.start()
        self.show_progress_window()

    def show_progress_window(self):
        self.progress_window = tk.Toplevel(self.master)
        self.progress_window.title("Progresso atual")
        self.progress_window.geometry("500x500")
        self.progress_window.configure(bg="#2c3e50")

        frame = ttk.Frame(self.progress_window, padding="30", style='TFrame')
        frame.grid(row=0, column=0, sticky=(
            tk.W, tk.E, tk.N, tk.S))
        frame.columnconfigure(0, weight=1)
        self.progress_window.columnconfigure(0, weight=1)
        self.progress_window.rowconfigure(0, weight=1)

        ttk.Label(frame, text="Progresso da Busca", font=(
            'Arial', 14, 'bold')).grid(column=0, row=0, pady=20)

        self.progress_bar = ttk.Progressbar(
            frame, orient="horizontal", length=400, mode="determinate", style="TProgressbar")
        self.progress_bar.grid(column=0, row=1, pady=20)

        self.progress_text = ttk.Label(frame, text="0%", font=('Arial', 12))
        self.progress_text.grid(column=0, row=2, pady=10)

        self.processo_text = ttk.Label(
            frame, text=f"Processo: {self.processo_num}", font=('Arial', 11))
        self.processo_text.grid(column=0, row=3, pady=10)

        self.last_result_text = ttk.Label(
            frame, text=f"Resultado: {self.last_result}", font=('Arial', 11))
        self.last_result_text.grid(column=0, row=4, pady=10)

        self.salvando_text = ttk.Label(frame, text="", font=('Arial', 10))
        self.salvando_text.grid(column=0, row=5, pady=10)

        self.processos_nao_encontrados_text = ttk.Label(
            frame, text="Processos não encontrados: 0", font=('Arial', 11))
        self.processos_nao_encontrados_text.grid(column=0, row=6, pady=10)

        self.total_processos_text = ttk.Label(
            frame, text="Total de processos: 0", font=('Arial', 11))
        self.total_processos_text.grid(column=0, row=7, pady=10)

        ttk.Label(frame, text="Logs:", font=('Arial', 12, 'bold')).grid(
            column=0, row=8, pady=10)
        self.log_text = tk.Text(frame, height=10, width=60)
        self.log_text.grid(column=0, row=9, pady=5)

        self.update_progress()

    def update_progress(self):
        self.progress_bar["value"] = self.porcentagem_concluida * 100
        self.progress_text["text"] = f"{round(self.porcentagem_concluida * 100, 2)}%"
        self.processo_text["text"] = f"Processo: {self.processo_num}"
        self.last_result_text["text"] = f"Resultado: {self.last_result}"
        self.salvando_text["text"] = self.salvando
        self.processos_nao_encontrados_text["text"] = f"Processos não encontrados: {self.processos_nao_encontrados}"
        self.total_processos_text["text"] = f"Total de processos: {self.total_processos}"

        if self.not_found_logs:
            self.log_text.delete(1.0, tk.END)
            for log in self.not_found_logs[-10:]:
                self.log_text.insert(tk.END, log + '\n')

        if self.porcentagem_concluida < 1:
            self.master.after(200, self.update_progress)
        else:
            ttk.Button(self.progress_window, text="Fechar", command=self.progress_window.destroy).grid(
                column=0, row=10, pady=20)

    def fazer_login_cdp(self, sb, usuario, senha):
        """Faz login usando CDP Mode para máxima proteção"""
        try:
            logging.info("Iniciando login com CDP Mode...")
            
            # Navegar para página de login
            sb.cdp.open("https://projudi.tjgo.jus.br/LogOn?PaginaAtual=-200")
            sb.sleep(3)
            
            # Inserir credenciais usando CDP
            sb.cdp.type("#login", usuario)
            sb.sleep(1)
            sb.cdp.type("#senha", senha)
            sb.sleep(1)
            
            # Clicar no botão de login
            sb.cdp.click("#formLogin input[type='submit']")
            sb.sleep(5)
            
            # Verificar se login foi bem-sucedido
            if sb.cdp.is_element_present("#menuPrinciapl"):
                logging.info("Login CDP realizado com sucesso!")
                return True
            else:
                logging.error("Falha no login CDP")
                return False
                
        except Exception as e:
            logging.error(f"Erro no login CDP: {e}")
            return False

    def extrair_dados_processo_cdp(self, sb, numero_processo):
        """Extrai dados usando CDP Mode - método mais avançado"""
        resultados = {'Processo Encontrado': 'Não'}

        try:
            logging.info(f"Processando processo com CDP: {numero_processo}")
            
            # Navegar para busca usando CDP
            sb.cdp.open("https://projudi.tjgo.jus.br/BuscaProcesso?PaginaAtual=4&TipoConsultaProcesso=24")
            sb.sleep(2)

            # Inserir número do processo
            sb.cdp.type("#ProcessoNumero", numero_processo)
            sb.sleep(1)

            # Clicar em mostrar todos os processos
            try:
                sb.cdp.click("button[onclick*='MostrarTodos']")
                sb.sleep(1)
            except:
                pass

            # Clicar em pesquisar
            sb.cdp.click("#divBotoesCentralizados input[type='submit']")
            sb.sleep(3)

            # Verificar se processo foi encontrado
            if sb.cdp.is_element_present("div[id*='resultado']") or sb.cdp.is_text_visible("Polo Ativo"):
                resultados['Processo Encontrado'] = 'Sim'
                logging.info(f"Processo {numero_processo} encontrado com CDP!")
                
                # Extrair dados
                try:
                    nome = sb.cdp.get_text("span:contains('Nome')")
                    if nome:
                        resultados['Nome Polo Ativo'] = nome.strip()
                except:
                    resultados['Nome Polo Ativo'] = ''
                
                try:
                    cpf = sb.cdp.get_text("span:contains('CPF')")
                    if cpf:
                        resultados['CPF Polo Ativo'] = cpf.strip()
                except:
                    resultados['CPF Polo Ativo'] = ''
            else:
                logging.warning(f"Processo {numero_processo} não encontrado")
                
            return resultados

        except Exception as e:
            logging.error(f"Erro CDP no processo {numero_processo}: {e}")
            return resultados

    def processar_planilha(self, usuario, senha):
        """Processa planilha usando CDP Mode"""
        try:
            df = pd.read_excel(self.excel_dir)
            
            # Verificações da planilha
            if df.empty or len(df.columns) == 0:
                messagebox.showerror("Erro", "A planilha está vazia ou não possui colunas.")
                return
            
            primeira_coluna = df.columns[0]
            logging.info(f"Usando a primeira coluna '{primeira_coluna}' como números de processo")
            
            df = df.rename(columns={primeira_coluna: 'Número do Processo'})
            df = df.dropna(subset=['Número do Processo'])
            
            if len(df) == 0:
                messagebox.showerror("Erro", "Não há números de processo válidos na primeira coluna.")
                return

            novas_colunas = ['Nome Polo Ativo', 'CPF Polo Ativo', 'Processo Encontrado']
            for coluna in novas_colunas:
                if coluna not in df.columns:
                    df[coluna] = ''
            
            logging.info(f"Planilha carregada: {len(df)} processos encontrados")

            # Usar SeleniumBase com CDP Mode - máxima proteção
            logging.info("Iniciando SeleniumBase com CDP Mode...")
            
            with SB(uc=True, test=True, headless=False, incognito=True, 
                   disable_csp=True, disable_ws=True, block_images=True,
                   chromium_arg="--disable-blink-features=AutomationControlled") as sb:
                
                logging.info("CDP Mode iniciado!")
                
                # Login usando CDP
                if not self.fazer_login_cdp(sb, usuario, senha):
                    messagebox.showerror("Erro", "Falha no login. Verifique suas credenciais.")
                    return

                total_registros = len(df)
                self.total_processos = total_registros
                
                for index, row in df.iterrows():
                    processo_num = str(row['Número do Processo'])
                    self.processo_num = processo_num
                    
                    resultados = self.extrair_dados_processo_cdp(sb, processo_num)

                    df.at[index, 'Processo Encontrado'] = resultados.get('Processo Encontrado', 'Não')
                    if resultados.get('Processo Encontrado') == 'Sim':
                        df.at[index, 'Nome Polo Ativo'] = resultados.get('Nome Polo Ativo', '')
                        df.at[index, 'CPF Polo Ativo'] = resultados.get('CPF Polo Ativo', '')
                    else:
                        self.processos_nao_encontrados += 1

                    self.porcentagem_concluida = (index + 1) / total_registros
                    self.last_result = f"Processo {processo_num} {'encontrado' if resultados.get('Processo Encontrado') == 'Sim' else 'não encontrado'}"

                    # Salvar a cada 50 registros
                    if (index + 1) % 50 == 0:
                        novo_nome_arquivo = f"Resultados_{datetime.now().strftime('%Y%m%d')}.xlsx"
                        script_dir = os.path.dirname(os.path.abspath(__file__))
                        output_path = os.path.join(script_dir, novo_nome_arquivo)
                        df.to_excel(output_path, index=False)
                        self.salvando = f"Salvando... Última vez salvo: {datetime.now().strftime('%H:%M')}"

                # Salvar resultado final
                novo_nome_arquivo = f"Resultados_{datetime.now().strftime('%Y%m%d')}.xlsx"
                script_dir = os.path.dirname(os.path.abspath(__file__))
                output_path = os.path.join(script_dir, novo_nome_arquivo)
                df.to_excel(output_path, index=False)
                
                messagebox.showinfo("Concluído", f"Processamento concluído!\nResultados salvos em {output_path}")

        except Exception as e:
            logging.error(f"Erro ao processar planilha: {str(e)}")
            logging.error(traceback.format_exc())
            messagebox.showerror("Erro", f"Ocorreu um erro: {str(e)}")


if __name__ == "__main__":
    root = tk.Tk()
    app = Application(master=root)
    app.mainloop()
