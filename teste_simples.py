#!/usr/bin/env python3
"""
Teste simples para verificar se as importações estão funcionando
"""

print("🔧 TESTE SIMPLES DE IMPORTAÇÕES")
print("=" * 40)

# Teste 1: Importações básicas
try:
    import tkinter as tk
    print("✅ tkinter: OK")
except ImportError as e:
    print(f"❌ tkinter: {e}")

try:
    import pandas as pd
    print(f"✅ pandas: {pd.__version__}")
except ImportError as e:
    print(f"❌ pandas: {e}")

try:
    from selenium import webdriver
    print("✅ selenium: OK")
except ImportError as e:
    print(f"❌ selenium: {e}")

try:
    import chromedriver_autoinstaller
    print("✅ chromedriver_autoinstaller: OK")
except ImportError as e:
    print(f"❌ chromedriver_autoinstaller: {e}")

# Teste 2: Importações anti-detecção (opcionais)
try:
    import undetected_chromedriver as uc
    print("✅ undetected_chromedriver: OK")
except ImportError as e:
    print(f"⚠️ undetected_chromedriver: {e} (usando fallback)")

try:
    from selenium_stealth import stealth
    print("✅ selenium_stealth: OK")
except ImportError as e:
    print(f"⚠️ selenium_stealth: {e} (usando fallback)")

print("\n" + "=" * 40)
print("✅ TESTE CONCLUÍDO!")
print("\nSe você vê esta mensagem, as importações básicas estão funcionando.")
print("O programa principal deve estar executando corretamente.")
print("\nPara usar o programa:")
print("1. Certifique-se de que a janela do programa está aberta")
print("2. Preencha usuário e senha")
print("3. Selecione a planilha Excel")
print("4. Clique em 'Iniciar Busca'")
