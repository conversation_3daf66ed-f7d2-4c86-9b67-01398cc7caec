# Busca Nome e CPF - Versão Anti-Detecção

Este programa foi atualizado com tecnologias profissionais para evitar detecção de automação e contornar sistemas anti-bot.

## 🚀 Melhorias Implementadas

### Tecnologias Anti-Detecção
- **undetected-chromedriver**: Driver Chrome customizado que não é detectado por sistemas anti-bot
- **selenium-stealth**: Camada adicional de stealth para mascarar automação
- **User-Agent Rotation**: Rotação aleatória de user-agents para simular diferentes navegadores
- **Human-like Delays**: Delays aleatórios que simulam comportamento humano
- **Typing Simulation**: Simulação de digitação humana com delays entre caracteres

### Configurações Avançadas
- Mais de 15 flags do Chrome para evitar detecção
- Configurações CDP (Chrome DevTools Protocol) para mascarar propriedades do navegador
- Timeouts aumentados para aguardar carregamentos
- Logs detalhados para monitoramento

## 📋 Pré-requisitos

- Python 3.6 ou superior
- Google Chrome instalado
- Conexão com internet

## 🔧 Instalação

1. **Instalar dependências automaticamente:**
   ```
   Clique duas vezes no arquivo: instalar_dependencias.bat
   ```

2. **Ou instalar manualmente:**
   ```bash
   pip install -r requirements.txt
   ```

## 📊 Como Usar

1. Execute o programa:
   ```bash
   python buscaNomeCPF.py
   ```

2. Preencha suas credenciais de login

3. Selecione a planilha Excel (primeira coluna = números de processo)

4. Clique em "Iniciar Busca"

## ⚡ Recursos Anti-Detecção

### Delays Inteligentes
- **Entre processos**: 5-10 segundos aleatórios
- **Entre ações**: 1-6 segundos aleatórios
- **Digitação**: 0.05-0.15 segundos entre caracteres
- **Carregamento**: 3-6 segundos após ações importantes

### Simulação Humana
- Digitação caractere por caractere
- Movimentos de mouse naturais
- Delays variáveis e imprevisíveis
- User-agents de navegadores reais

### Configurações Chrome
- Desabilitação de recursos de automação
- Mascaramento de propriedades do navegador
- Configurações de segurança otimizadas
- Flags anti-detecção profissionais

## 📝 Logs e Monitoramento

O programa gera logs detalhados em:
- `busca_nome_cpf.log`: Log completo de todas as operações
- Screenshots automáticos em caso de erro (pasta `screenshots/`)

## 🛡️ Recursos de Segurança

- Fallback automático para driver normal em caso de erro
- Salvamento automático a cada 50 processos
- Salvamento parcial em caso de interrupção
- Tratamento robusto de erros

## 📈 Performance

- Processamento otimizado com delays inteligentes
- Menor chance de bloqueio por sistemas anti-bot
- Maior taxa de sucesso em sites protegidos
- Monitoramento em tempo real do progresso

## 🔍 Troubleshooting

### Se ainda houver detecção:
1. Aumente os delays no código (variáveis `min_seconds` e `max_seconds`)
2. Adicione mais user-agents na lista `USER_AGENTS`
3. Execute em horários de menor tráfego
4. Use conexão de internet diferente

### Problemas comuns:
- **Chrome não abre**: Reinstale o Google Chrome
- **Erro de dependências**: Execute `instalar_dependencias.bat` novamente
- **Timeout**: Aumente os valores de timeout no código

## 📞 Suporte

Para problemas ou melhorias, verifique os logs em `busca_nome_cpf.log` para identificar a causa do erro.

---

**Nota**: Este programa utiliza as melhores práticas de automação web para evitar detecção, mas sempre respeite os termos de uso dos sites que você está acessando.
