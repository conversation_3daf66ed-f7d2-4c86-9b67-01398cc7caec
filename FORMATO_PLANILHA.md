# 📊 FORMATO DA PLANILHA - GUIA COMPLETO

## 🎯 **MUDANÇA IMPORTANTE**

✅ **AGORA É MAIS SIMPLES!** O programa foi atualizado para usar automaticamente a **primeira coluna** como números de processo.

❌ **Não é mais necessário** ter uma coluna específica chamada "Número do Processo"

---

## 📋 **FORMATO ACEITO**

### ✅ **Qualquer planilha Excel (.xlsx ou .xls) com:**
- **Primeira coluna**: Números de processo (obrigatório)
- **Outras colunas**: Opcionais (serão preservadas)

### 📝 **Exemplos de Formatos Aceitos:**

#### Exemplo 1: Simples
```
| Processos                    |
|------------------------------|
| 1234567-89.2023.8.09.0001   |
| 2345678-90.2023.8.09.0002   |
| 3456789-01.2023.8.09.0003   |
```

#### Exemplo 2: Com informações adicionais
```
| Números                      | Cliente    | Observações |
|------------------------------|------------|-------------|
| 1234567-89.2023.8.09.0001   | João Silva | Urgente     |
| 2345678-90.2023.8.09.0002   | Maria José | Normal      |
| 3456789-01.2023.8.09.0003   | Pedro Lima | Revisar     |
```

#### Exemplo 3: Qualquer nome de coluna
```
| Processo_Numero              | Data       | Status      |
|------------------------------|------------|-------------|
| 1234567-89.2023.8.09.0001   | 01/01/2024 | Ativo       |
| 2345678-90.2023.8.09.0002   | 02/01/2024 | Pendente    |
```

---

## 🔧 **COMO O PROGRAMA FUNCIONA**

### 1. **Leitura Automática**
- O programa lê automaticamente a **primeira coluna**
- Não importa o nome da coluna
- Remove linhas vazias automaticamente

### 2. **Processamento**
- Usa os valores da primeira coluna como números de processo
- Adiciona 3 novas colunas com os resultados:
  - `Nome Polo Ativo`
  - `CPF Polo Ativo` 
  - `Processo Encontrado`

### 3. **Resultado Final**
```
| Número do Processo           | Nome Polo Ativo | CPF Polo Ativo | Processo Encontrado |
|------------------------------|-----------------|----------------|-------------------|
| 1234567-89.2023.8.09.0001   | João Silva      | 123.456.789-00 | Sim               |
| 2345678-90.2023.8.09.0002   | Maria José      | 987.654.321-00 | Sim               |
| 3456789-01.2023.8.09.0003   |                 |                | Não               |
```

---

## 🚀 **CRIAR PLANILHA DE EXEMPLO**

### Método 1: Script Automático
```bash
python exemplo_planilha.py
```
Isso criará: `exemplo_processos.xlsx`

### Método 2: Manual no Excel
1. Abra o Excel
2. Na coluna A, digite os números de processo
3. Salve como `.xlsx`

### Método 3: Converter arquivo existente
- Se você já tem uma planilha, apenas certifique-se de que os números de processo estão na **primeira coluna**

---

## ✅ **FORMATOS DE NÚMERO DE PROCESSO ACEITOS**

O programa aceita qualquer formato de texto na primeira coluna:

```
✅ 1234567-89.2023.8.09.0001
✅ 1234567892023809001
✅ 1234567-89.2023.809.0001
✅ Processo 1234567-89.2023.8.09.0001
✅ 123456789
```

---

## ❌ **PROBLEMAS COMUNS E SOLUÇÕES**

### Erro: "Planilha está vazia"
**Causa**: Arquivo Excel corrompido ou sem dados
**Solução**: 
- Verifique se o arquivo abre no Excel
- Certifique-se de que há dados na primeira coluna

### Erro: "Primeira coluna não contém dados válidos"
**Causa**: Primeira coluna está vazia ou só tem células vazias
**Solução**:
- Mova os números de processo para a coluna A
- Remova linhas vazias no início

### Erro: "Não há números de processo válidos"
**Causa**: Todas as células da primeira coluna estão vazias
**Solução**:
- Verifique se os dados estão na primeira coluna
- Certifique-se de que não há espaços em branco apenas

---

## 📁 **LOCALIZAÇÃO DOS ARQUIVOS**

### Arquivos de Entrada:
- Sua planilha: Qualquer local
- Formato: `.xlsx` ou `.xls`

### Arquivos de Saída:
- `Resultados_YYYYMMDD.xlsx`: Pasta do programa
- `busca_nome_cpf.log`: Pasta do programa
- `screenshots/`: Pasta do programa (se houver erros)

---

## 🎯 **RESUMO RÁPIDO**

1. **Prepare sua planilha**: Números de processo na primeira coluna
2. **Execute o programa**: `python buscaNomeCPF.py`
3. **Selecione a planilha**: Qualquer arquivo Excel
4. **Inicie a busca**: O programa fará o resto automaticamente

**✅ Agora é muito mais simples usar qualquer planilha Excel!**
