Metadata-Version: 2.1
Name: chromedriver-autoinstaller
Version: 0.6.2
Summary: Automatically install chromedriver that supports the currently installed version of chrome.
Home-page: https://github.com/yeongbin-jo/python-chromedriver-autoinstaller
Author: <PERSON><PERSON><PERSON> Jo
Author-email: <EMAIL>
License: MIT
Keywords: chromedriver chrome chromium selenium
Classifier: Development Status :: 5 - Production/Stable
Classifier: Topic :: Software Development :: Testing
Classifier: Topic :: System :: Installation/Setup
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Requires-Python: >=3.6
Description-Content-Type: text/markdown
License-File: LICENSE

# chromedriver-autoinstaller
Automatically download and install [chromedriver](https://chromedriver.chromium.org/) that supports the currently installed version of chrome. This installer supports Linux, MacOS and Windows operating systems.

## Installation

```bash
pip install chromedriver-autoinstaller
```

## Usage
Just type `import chromedriver_autoinstaller` in the module you want to use chromedriver.

## Example
```
from selenium import webdriver
import chromedriver_autoinstaller


chromedriver_autoinstaller.install()  # Check if the current version of chromedriver exists
                                      # and if it doesn't exist, download it automatically,
                                      # then add chromedriver to path

driver = webdriver.Chrome()
driver.get("http://www.python.org")
assert "Python" in driver.title
```
