import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import threading
from datetime import datetime
import logging
import traceback
import os

# Configuração do logging
logging.basicConfig(filename='busca_nome_cpf.log', level=logging.DEBUG,
                    format='%(asctime)s - %(levelname)s - %(message)s')

# Importar SeleniumBase
try:
    from seleniumbase import SB
    SELENIUMBASE_AVAILABLE = True
    logging.info("SeleniumBase disponível - usando UC Mode para máxima proteção anti-detecção")
except ImportError:
    SELENIUMBASE_AVAILABLE = False
    logging.error("SeleniumBase não disponível - instale com: pip install seleniumbase")


class Application(tk.Frame):
    def __init__(self, master=None):
        super().__init__(master)
        self.master = master
        self.master.title("Busca Nome e CPF - SeleniumBase Anti-Detecção")
        self.master.geometry("800x700")
        self.master.configure(bg="#2c3e50")
        self.excel_dir = None
        self.porcentagem_concluida = 0
        self.processo_num = "Inicializando..."
        self.last_result = "Buscando..."
        self.salvando = ""
        self.processos_nao_encontrados = 0
        self.total_processos = 0
        self.not_found_logs = []
        self.create_widgets()

    def create_widgets(self):
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TFrame', background="#2c3e50")
        style.configure('TButton', font=('Arial', 10, 'bold'), borderwidth=1,
                        background="#3498db", foreground="white")
        style.configure('TLabel', font=('Arial', 11),
                        background="#2c3e50", foreground="white")
        style.configure('TEntry', font=('Arial', 10),
                        fieldbackground="#34495e", foreground="white")

        main_frame = ttk.Frame(
            self.master, padding="30 30 30 30", style='TFrame')
        main_frame.grid(row=0, column=0, sticky=(
            tk.W, tk.E, tk.N, tk.S))
        main_frame.columnconfigure(0, weight=1)
        self.master.columnconfigure(0, weight=1)
        self.master.rowconfigure(0, weight=1)

        ttk.Label(main_frame, text="Busca Nome e CPF - SeleniumBase", font=(
            'Arial', 16, 'bold')).grid(column=0, row=0, columnspan=2, pady=20)

        ttk.Label(main_frame, text="Usuário:").grid(
            column=0, row=1, sticky=tk.W, pady=5)
        self.entrada_usuario = ttk.Entry(main_frame)
        self.entrada_usuario.grid(
            column=0, row=2, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(main_frame, text="Senha:").grid(
            column=0, row=3, sticky=tk.W, pady=5)
        self.entrada_senha = ttk.Entry(main_frame, show="*")
        self.entrada_senha.grid(
            column=0, row=4, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(main_frame, text="Selecione a planilha Excel (primeira coluna = números de processo):").grid(
            column=0, row=5, sticky=tk.W, pady=10)
        self.excel_dir_entry = ttk.Entry(main_frame, width=50)
        self.excel_dir_entry.grid(
            column=0, row=6, sticky=(tk.W, tk.E), pady=5)
        ttk.Button(main_frame, text="Selecionar Arquivo",
                   command=self.select_file).grid(column=1, row=6, sticky=tk.W, padx=5, pady=5)

        ttk.Button(main_frame, text="Iniciar Busca", command=self.start_progress).grid(
            column=0, row=7, sticky=(tk.W, tk.E), pady=20, columnspan=2)

    def select_file(self):
        file_path = filedialog.askopenfilename(
            filetypes=[("Excel files", "*.xlsx *.xls")])
        self.excel_dir_entry.delete(0, tk.END)
        self.excel_dir_entry.insert(0, file_path)

    def start_progress(self):
        if not SELENIUMBASE_AVAILABLE:
            messagebox.showerror(
                "Erro", "SeleniumBase não está instalado. Execute: pip install seleniumbase")
            return
            
        self.excel_dir = self.excel_dir_entry.get()
        usuario = self.entrada_usuario.get()
        senha = self.entrada_senha.get()

        if not self.excel_dir:
            messagebox.showerror(
                "Erro", "Por favor, selecione um arquivo Excel.")
            return
        if not usuario or not senha:
            messagebox.showerror(
                "Erro", "Por favor, preencha o usuário e senha.")
            return

        t = threading.Thread(target=self.processar_planilha,
                             args=(usuario, senha))
        t.start()
        self.show_progress_window()

    def show_progress_window(self):
        self.progress_window = tk.Toplevel(self.master)
        self.progress_window.title("Progresso atual")
        self.progress_window.geometry("500x500")
        self.progress_window.configure(bg="#2c3e50")

        frame = ttk.Frame(self.progress_window, padding="30", style='TFrame')
        frame.grid(row=0, column=0, sticky=(
            tk.W, tk.E, tk.N, tk.S))
        frame.columnconfigure(0, weight=1)
        self.progress_window.columnconfigure(0, weight=1)
        self.progress_window.rowconfigure(0, weight=1)

        ttk.Label(frame, text="Progresso da Busca", font=(
            'Arial', 14, 'bold')).grid(column=0, row=0, pady=20)

        self.progress_bar = ttk.Progressbar(
            frame, orient="horizontal", length=400, mode="determinate", style="TProgressbar")
        self.progress_bar.grid(column=0, row=1, pady=20)

        self.progress_text = ttk.Label(frame, text="0%", font=('Arial', 12))
        self.progress_text.grid(column=0, row=2, pady=10)

        self.processo_text = ttk.Label(
            frame, text=f"Processo: {self.processo_num}", font=('Arial', 11))
        self.processo_text.grid(column=0, row=3, pady=10)

        self.last_result_text = ttk.Label(
            frame, text=f"Resultado: {self.last_result}", font=('Arial', 11))
        self.last_result_text.grid(column=0, row=4, pady=10)

        self.salvando_text = ttk.Label(frame, text="", font=('Arial', 10))
        self.salvando_text.grid(column=0, row=5, pady=10)

        self.processos_nao_encontrados_text = ttk.Label(
            frame, text="Processos não encontrados: 0", font=('Arial', 11))
        self.processos_nao_encontrados_text.grid(column=0, row=6, pady=10)

        self.total_processos_text = ttk.Label(
            frame, text="Total de processos: 0", font=('Arial', 11))
        self.total_processos_text.grid(column=0, row=7, pady=10)

        ttk.Label(frame, text="Logs:", font=('Arial', 12, 'bold')).grid(
            column=0, row=8, pady=10)
        self.log_text = tk.Text(frame, height=10, width=60)
        self.log_text.grid(column=0, row=9, pady=5)

        self.update_progress()

    def update_progress(self):
        try:
            # Verificar se a janela ainda existe
            if not hasattr(self, 'progress_window') or not self.progress_window.winfo_exists():
                return

            self.progress_bar["value"] = self.porcentagem_concluida * 100
            self.progress_text["text"] = f"{round(self.porcentagem_concluida * 100, 2)}%"
            self.processo_text["text"] = f"Processo: {self.processo_num}"
            self.last_result_text["text"] = f"Resultado: {self.last_result}"
            self.salvando_text["text"] = self.salvando
            self.processos_nao_encontrados_text["text"] = f"Processos não encontrados: {self.processos_nao_encontrados}"
            self.total_processos_text["text"] = f"Total de processos: {self.total_processos}"

            if self.not_found_logs:
                self.log_text.delete(1.0, tk.END)
                for log in self.not_found_logs[-10:]:
                    self.log_text.insert(tk.END, log + '\n')

            if self.porcentagem_concluida < 1:
                self.master.after(200, self.update_progress)
            else:
                ttk.Button(self.progress_window, text="Fechar", command=self.progress_window.destroy).grid(
                    column=0, row=10, pady=20)
        except tk.TclError:
            # Janela foi fechada, parar updates
            return
        except Exception as e:
            logging.error(f"Erro no update_progress: {e}")
            return

    def extrair_dados_processo(self, sb, numero_processo):
        """Extrai dados do processo usando SeleniumBase com métodos anti-detecção avançados"""
        resultados = {'Processo Encontrado': 'Não'}

        try:
            logging.info(f"Processando processo: {numero_processo}")

            # Usar uc_open_with_reconnect para navegação stealth
            sb.uc_open_with_reconnect("https://projudi.tjgo.jus.br/BuscaProcesso?PaginaAtual=4&TipoConsultaProcesso=24", reconnect_time=2)

            # Aguardar carregamento completo
            sb.sleep(2)

            # Inserir o número do processo usando método stealth
            sb.uc_type('//*[@id="ProcessoNumero"]', numero_processo)

            # Clicar no botão para mostrar todos os processos usando uc_click
            try:
                sb.uc_click('/html/body/div/form/div/fieldset/div[5]/fieldset/div[1]/label/button[2]')
                sb.sleep(1)
            except Exception as e:
                message = f"Processo {numero_processo}: Botão para mostrar todos os processos não encontrado."
                logging.warning(message)
                self.not_found_logs.append(message)

            # Clicar no botão "Pesquisar" usando uc_click
            sb.uc_click('//*[@id="divBotoesCentralizados"]/input[1]')

            # Aguardar resultado
            sb.sleep(3)

            # Verificar se o processo foi encontrado
            try:
                sb.wait_for_element('/html/body/div[2]/form', timeout=10)
                resultados['Processo Encontrado'] = 'Sim'
                logging.info(f"Processo {numero_processo} encontrado!")
            except Exception:
                message = f"Processo {numero_processo} não encontrado."
                logging.warning(message)
                self.not_found_logs.append(message)
                resultados['Processo Encontrado'] = 'Não'
                return resultados

            # Extrair informações do polo ativo
            try:
                nome_polo_ativo = sb.get_text('/html/body/div[2]/form/div[1]/fieldset/fieldset[2]/fieldset[1]/fieldset/span[1]').strip()
                resultados['Nome Polo Ativo'] = nome_polo_ativo
                logging.info(f"Nome encontrado: {nome_polo_ativo}")
            except Exception as e:
                message = f"Processo {numero_processo}: Nome Polo Ativo não encontrado."
                logging.warning(message)
                self.not_found_logs.append(message)
                resultados['Nome Polo Ativo'] = ''

            try:
                cpf_polo_ativo = sb.get_text('/html/body/div[2]/form/div[1]/fieldset/fieldset[2]/fieldset[1]/fieldset/span[2]').strip()
                resultados['CPF Polo Ativo'] = cpf_polo_ativo
                logging.info(f"CPF encontrado: {cpf_polo_ativo}")
            except Exception as e:
                message = f"Processo {numero_processo}: CPF Polo Ativo não encontrado."
                logging.warning(message)
                self.not_found_logs.append(message)
                resultados['CPF Polo Ativo'] = ''

            return resultados

        except Exception as e:
            message = f"Erro ao processar processo {numero_processo}: {str(e)}"
            logging.error(message)
            logging.error(traceback.format_exc())
            self.not_found_logs.append(message)
            return resultados

    def capture_screenshot(self, sb, filename):
        """Captura screenshot usando SeleniumBase"""
        try:
            if not os.path.exists('screenshots'):
                os.makedirs('screenshots')
            sb.save_screenshot(f"screenshots/{filename}.png")
            logging.info(f"Screenshot salvo: {filename}.png")
        except Exception as e:
            logging.error(f"Erro ao capturar screenshot: {str(e)}")

    def processar_planilha(self, usuario, senha):
        """Processa a planilha usando SeleniumBase UC Mode"""
        try:
            df = pd.read_excel(self.excel_dir)
            
            # Verificar se a planilha tem pelo menos uma coluna
            if df.empty or len(df.columns) == 0:
                messagebox.showerror(
                    "Erro", "A planilha está vazia ou não possui colunas.")
                return
            
            # Usar a primeira coluna como números de processo
            primeira_coluna = df.columns[0]
            logging.info(f"Usando a primeira coluna '{primeira_coluna}' como números de processo")
            
            # Renomear a primeira coluna para padronizar
            df = df.rename(columns={primeira_coluna: 'Número do Processo'})
            
            # Verificar se há dados na primeira coluna
            if df['Número do Processo'].isna().all():
                messagebox.showerror(
                    "Erro", "A primeira coluna não contém dados válidos.")
                return
            
            # Remover linhas vazias na primeira coluna
            df = df.dropna(subset=['Número do Processo'])
            
            if len(df) == 0:
                messagebox.showerror(
                    "Erro", "Não há números de processo válidos na primeira coluna.")
                return

            novas_colunas = ['Nome Polo Ativo', 'CPF Polo Ativo', 'Processo Encontrado']
            for coluna in novas_colunas:
                if coluna not in df.columns:
                    df[coluna] = ''
            
            logging.info(f"Planilha carregada: {len(df)} processos encontrados na primeira coluna")

            # Usar SeleniumBase com UC Mode e configurações anti-detecção máximas
            logging.info("Iniciando SeleniumBase com UC Mode e CDP Mode...")

            with SB(uc=True, test=True, headless=False, incognito=True,
                   disable_csp=True, disable_ws=True, block_images=True) as sb:
                logging.info("SeleniumBase UC Mode iniciado com sucesso!")

                # Login usando métodos stealth
                logging.info("Fazendo login com métodos anti-detecção...")

                # Usar uc_open_with_reconnect para máxima proteção
                sb.uc_open_with_reconnect("https://projudi.tjgo.jus.br/LogOn?PaginaAtual=-200", reconnect_time=3)

                # Aguardar carregamento completo
                sb.sleep(3)

                # Inserir credenciais usando métodos stealth
                sb.uc_type('//*[@id="login"]', usuario)
                sb.sleep(1)
                sb.uc_type('//*[@id="senha"]', senha)
                sb.sleep(1)

                # Clicar no botão de login usando uc_click
                sb.uc_click('//*[@id="formLogin"]/div[4]/input[1]')

                # Aguardar processamento do login
                sb.sleep(5)

                # Verificar se o login foi bem-sucedido
                try:
                    sb.wait_for_element('//*[@id="menuPrinciapl"]/ul[2]/li', timeout=15)
                    logging.info("Login realizado com sucesso!")
                except Exception as e:
                    logging.error(f"Erro no login: {e}")
                    # Capturar screenshot para debug
                    self.capture_screenshot(sb, "erro_login")
                    messagebox.showerror("Erro", "Falha no login. Verifique suas credenciais ou se há detecção de bot.")
                    return

                total_registros = len(df)
                self.total_processos = total_registros
                logging.info(f"Iniciando processamento de {total_registros} processos...")
                
                for index, row in df.iterrows():
                    processo_num = str(row['Número do Processo'])
                    self.processo_num = processo_num
                    logging.info(f"Processando processo {index + 1}/{total_registros}: {processo_num}")
                    
                    resultados = self.extrair_dados_processo(sb, processo_num)

                    df.at[index, 'Processo Encontrado'] = resultados.get('Processo Encontrado', 'Não')
                    if resultados.get('Processo Encontrado') == 'Sim':
                        df.at[index, 'Nome Polo Ativo'] = resultados.get('Nome Polo Ativo', '')
                        df.at[index, 'CPF Polo Ativo'] = resultados.get('CPF Polo Ativo', '')
                        logging.info(f"Processo {processo_num} encontrado com sucesso")
                    else:
                        self.processos_nao_encontrados += 1
                        logging.warning(f"Processo {processo_num} não encontrado")

                    # Atualizar progresso
                    self.porcentagem_concluida = (index + 1) / total_registros
                    self.last_result = f"Processo {processo_num} {'encontrado' if resultados.get('Processo Encontrado') == 'Sim' else 'não encontrado'}"

                    # Salvar a cada 50 registros processados
                    if (index + 1) % 50 == 0:
                        novo_nome_arquivo = f"Resultados_{datetime.now().strftime('%Y%m%d')}.xlsx"
                        script_dir = os.path.dirname(os.path.abspath(__file__))
                        output_path = os.path.join(script_dir, novo_nome_arquivo)
                        df.to_excel(output_path, index=False)
                        self.salvando = f"Salvando... Última vez salvo: {datetime.now().strftime('%H:%M')}"
                        logging.info(f"Salvando... Última vez salvo: {datetime.now().strftime('%H:%M')}")

                # Salvar a planilha final
                novo_nome_arquivo = f"Resultados_{datetime.now().strftime('%Y%m%d')}.xlsx"
                script_dir = os.path.dirname(os.path.abspath(__file__))
                output_path = os.path.join(script_dir, novo_nome_arquivo)
                df.to_excel(output_path, index=False)
                
                logging.info("Processamento concluído com sucesso!")
                messagebox.showinfo(
                    "Concluído", f"Processamento da planilha concluído com sucesso!\nResultados salvos em {output_path}")

        except Exception as e:
            logging.error(f"Erro ao processar a planilha: {str(e)}")
            logging.error(traceback.format_exc())
            messagebox.showerror(
                "Erro", f"Ocorreu um erro ao processar a planilha: {str(e)}")


if __name__ == "__main__":
    root = tk.Tk()
    app = Application(master=root)
    app.mainloop()
