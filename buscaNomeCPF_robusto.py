import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import threading
from datetime import datetime
import logging
import traceback
import os
import subprocess
import sys

# Configuração do logging
logging.basicConfig(filename='busca_nome_cpf.log', level=logging.DEBUG,
                    format='%(asctime)s - %(levelname)s - %(message)s')

class Application(tk.Frame):
    def __init__(self, master=None):
        super().__init__(master)
        self.master = master
        self.master.title("Busca Nome e CPF - SeleniumBase UC Mode Robusto")
        self.master.geometry("800x600")
        self.master.configure(bg="#2c3e50")
        self.excel_dir = None
        self.create_widgets()

    def create_widgets(self):
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TFrame', background="#2c3e50")
        style.configure('TButton', font=('Arial', 10, 'bold'), borderwidth=1,
                        background="#3498db", foreground="white")
        style.configure('TLabel', font=('Arial', 11),
                        background="#2c3e50", foreground="white")
        style.configure('TEntry', font=('Arial', 10),
                        fieldbackground="#34495e", foreground="white")

        main_frame = ttk.Frame(self.master, padding="30 30 30 30", style='TFrame')
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        main_frame.columnconfigure(0, weight=1)
        self.master.columnconfigure(0, weight=1)
        self.master.rowconfigure(0, weight=1)

        ttk.Label(main_frame, text="Busca Nome e CPF - SeleniumBase UC Mode", font=(
            'Arial', 16, 'bold')).grid(column=0, row=0, columnspan=2, pady=20)

        ttk.Label(main_frame, text="Usuário:").grid(column=0, row=1, sticky=tk.W, pady=5)
        self.entrada_usuario = ttk.Entry(main_frame)
        self.entrada_usuario.grid(column=0, row=2, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(main_frame, text="Senha:").grid(column=0, row=3, sticky=tk.W, pady=5)
        self.entrada_senha = ttk.Entry(main_frame, show="*")
        self.entrada_senha.grid(column=0, row=4, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(main_frame, text="Selecione a planilha Excel (primeira coluna = números de processo):").grid(
            column=0, row=5, sticky=tk.W, pady=10)
        self.excel_dir_entry = ttk.Entry(main_frame, width=50)
        self.excel_dir_entry.grid(column=0, row=6, sticky=(tk.W, tk.E), pady=5)
        ttk.Button(main_frame, text="Selecionar Arquivo",
                   command=self.select_file).grid(column=1, row=6, sticky=tk.W, padx=5, pady=5)

        ttk.Button(main_frame, text="Iniciar Busca", command=self.start_progress).grid(
            column=0, row=7, sticky=(tk.W, tk.E), pady=20, columnspan=2)

        # Status e logs
        self.status_label = ttk.Label(main_frame, text="Pronto para iniciar", font=('Arial', 10))
        self.status_label.grid(column=0, row=8, columnspan=2, pady=10)

        ttk.Label(main_frame, text="Logs:", font=('Arial', 12, 'bold')).grid(
            column=0, row=9, sticky=tk.W, pady=(20, 5))
        
        self.log_text = tk.Text(main_frame, height=8, width=80, bg="#34495e", fg="white")
        self.log_text.grid(column=0, row=10, columnspan=2, pady=5, sticky=(tk.W, tk.E))

        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.log_text.yview)
        scrollbar.grid(column=2, row=10, sticky=(tk.N, tk.S))
        self.log_text.configure(yscrollcommand=scrollbar.set)

    def log_message(self, message):
        """Adiciona mensagem ao log visual"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.master.update()

    def select_file(self):
        file_path = filedialog.askopenfilename(filetypes=[("Excel files", "*.xlsx *.xls")])
        self.excel_dir_entry.delete(0, tk.END)
        self.excel_dir_entry.insert(0, file_path)

    def start_progress(self):
        self.excel_dir = self.excel_dir_entry.get()
        usuario = self.entrada_usuario.get()
        senha = self.entrada_senha.get()

        if not self.excel_dir:
            messagebox.showerror("Erro", "Por favor, selecione um arquivo Excel.")
            return
        if not usuario or not senha:
            messagebox.showerror("Erro", "Por favor, preencha o usuário e senha.")
            return

        self.log_text.delete(1.0, tk.END)
        self.log_message("Iniciando processamento...")
        
        t = threading.Thread(target=self.processar_planilha, args=(usuario, senha))
        t.start()

    def processar_planilha(self, usuario, senha):
        """Processa a planilha usando SeleniumBase UC Mode"""
        try:
            self.log_message("Carregando planilha...")
            
            # Carregar planilha
            df = pd.read_excel(self.excel_dir)
            
            if df.empty or len(df.columns) == 0:
                self.log_message("❌ Planilha vazia!")
                messagebox.showerror("Erro", "A planilha está vazia ou não possui colunas.")
                return
            
            primeira_coluna = df.columns[0]
            df = df.rename(columns={primeira_coluna: 'Número do Processo'})
            df = df.dropna(subset=['Número do Processo'])
            
            if len(df) == 0:
                self.log_message("❌ Nenhum processo válido encontrado!")
                messagebox.showerror("Erro", "Não há números de processo válidos na primeira coluna.")
                return

            processos = df['Número do Processo'].tolist()
            self.log_message(f"✅ {len(processos)} processos carregados")

            # Criar script SeleniumBase mais robusto
            self.log_message("Criando script SeleniumBase...")
            
            script_content = f'''
from seleniumbase import BaseCase
import pandas as pd
from datetime import datetime
import os
import time

class TestBuscaProcessos(BaseCase):
    
    def test_buscar_processos(self):
        """Busca processos usando UC Mode com tratamento robusto de erros"""
        
        print("🚀 Iniciando SeleniumBase UC Mode...")
        
        # Lista de processos
        processos = {processos}
        resultados = []
        
        try:
            # Login com retry
            print("📝 Fazendo login...")
            for tentativa in range(3):
                try:
                    self.uc_open_with_reconnect("https://projudi.tjgo.jus.br/LogOn?PaginaAtual=-200", reconnect_time=5)
                    self.sleep(3)
                    
                    # Verificar se a página carregou
                    if not self.is_element_present("#login"):
                        print(f"⚠️ Tentativa {{tentativa + 1}}: Página não carregou corretamente")
                        continue
                    
                    self.uc_type("#login", "{usuario}")
                    self.sleep(1)
                    self.uc_type("#senha", "{senha}")
                    self.sleep(1)
                    self.uc_click("input[type='submit']")
                    self.sleep(5)
                    
                    # Verificar login
                    if self.is_element_present("#menuPrinciapl"):
                        print("✅ Login realizado com sucesso!")
                        break
                    else:
                        print(f"⚠️ Tentativa {{tentativa + 1}}: Login falhou")
                        
                except Exception as e:
                    print(f"⚠️ Tentativa {{tentativa + 1}} falhou: {{e}}")
                    if tentativa == 2:
                        raise Exception("Falha no login após 3 tentativas")
            
            # Processar cada processo
            for i, processo in enumerate(processos, 1):
                print(f"🔍 Processando {{i}}/{{len(processos)}}: {{processo}}")
                
                resultado = {{
                    'Número do Processo': processo,
                    'Processo Encontrado': 'Não',
                    'Nome Polo Ativo': '',
                    'CPF Polo Ativo': ''
                }}
                
                try:
                    # Verificar se o browser ainda está ativo
                    if not self.driver.current_window_handle:
                        print("❌ Browser foi fechado! Tentando reconectar...")
                        self.uc_open_with_reconnect("https://projudi.tjgo.jus.br/LogOn?PaginaAtual=-200")
                        continue
                    
                    # Navegar para busca com retry
                    for tentativa_busca in range(2):
                        try:
                            self.uc_open_with_reconnect("https://projudi.tjgo.jus.br/BuscaProcesso?PaginaAtual=4&TipoConsultaProcesso=24", reconnect_time=3)
                            self.sleep(2)
                            
                            if self.is_element_present("#ProcessoNumero"):
                                break
                        except:
                            if tentativa_busca == 1:
                                print(f"❌ Falha ao carregar página de busca para {{processo}}")
                                continue
                    
                    # Inserir número do processo
                    self.uc_type("#ProcessoNumero", str(processo))
                    self.sleep(1)
                    
                    # Clicar em mostrar todos os processos
                    try:
                        if self.is_element_present("button[onclick*='MostrarTodos']"):
                            self.uc_click("button[onclick*='MostrarTodos']")
                            self.sleep(1)
                    except:
                        pass
                    
                    # Clicar em pesquisar
                    self.uc_click("#divBotoesCentralizados input[type='submit']")
                    self.sleep(4)
                    
                    # Verificar resultado
                    try:
                        page_source = self.get_page_source()
                        
                        # Verificar se há mensagem de acesso predatório
                        if "predatório" in page_source.lower() or "predatory" in page_source.lower():
                            print(f"❌ Processo {{processo}}: ACESSO PREDATÓRIO DETECTADO!")
                            resultado['Processo Encontrado'] = 'Erro - Acesso Predatório'
                        elif "Polo Ativo" in page_source or self.is_text_visible("Polo Ativo"):
                            resultado['Processo Encontrado'] = 'Sim'
                            print(f"✅ Processo {{processo}}: Encontrado!")
                            
                            # Extrair dados básicos
                            try:
                                spans = self.find_elements("span")
                                for span in spans:
                                    text = span.text.strip()
                                    if text and len(text) > 5:
                                        if not resultado['Nome Polo Ativo'] and not any(char.isdigit() for char in text[:3]):
                                            resultado['Nome Polo Ativo'] = text
                                        elif "." in text and "-" in text and len(text) >= 11:
                                            resultado['CPF Polo Ativo'] = text
                            except:
                                pass
                        else:
                            print(f"ℹ️ Processo {{processo}}: Não encontrado")
                    
                    except Exception as e:
                        print(f"❌ Erro ao verificar resultado para {{processo}}: {{e}}")
                
                except Exception as e:
                    print(f"❌ Erro geral no processo {{processo}}: {{e}}")
                
                resultados.append(resultado)
                
                # Salvar a cada 5 processos
                if i % 5 == 0:
                    self.salvar_resultados(resultados)
            
            # Salvar resultados finais
            self.salvar_resultados(resultados)
            print("🎉 Processamento concluído!")
            
        except Exception as e:
            print(f"❌ Erro crítico: {{e}}")
            if resultados:
                self.salvar_resultados(resultados)
    
    def salvar_resultados(self, resultados):
        """Salva os resultados em Excel"""
        try:
            df_resultado = pd.DataFrame(resultados)
            nome_arquivo = f"Resultados_{{datetime.now().strftime('%Y%m%d_%H%M%S')}}.xlsx"
            df_resultado.to_excel(nome_arquivo, index=False)
            print(f"💾 Resultados salvos em: {{nome_arquivo}}")
        except Exception as e:
            print(f"❌ Erro ao salvar: {{e}}")
'''

            # Salvar script
            script_filename = 'test_busca_robusta.py'
            with open(script_filename, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            self.log_message("Executando SeleniumBase UC Mode...")
            self.status_label.config(text="🚀 Chrome abrindo com UC Mode...")
            
            # Executar script com UC Mode
            cmd = [sys.executable, '-m', 'pytest', '--uc', '-v', '-s', '--tb=short', script_filename]
            
            self.log_message(f"Comando: {' '.join(cmd)}")
            
            # Executar em subprocess com output em tempo real
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True,
                cwd=os.getcwd()
            )
            
            self.log_message("✅ Chrome deve ter aberto! Processando...")
            
            # Ler output em tempo real
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    self.log_message(output.strip())
            
            # Aguardar conclusão
            process.wait()
            
            if process.returncode == 0:
                self.status_label.config(text="✅ Processamento concluído!")
                self.log_message("🎉 Processamento concluído com sucesso!")
                messagebox.showinfo("Sucesso", "Processamento concluído! Verifique os arquivos de resultado.")
            else:
                self.status_label.config(text="⚠️ Processamento com problemas")
                self.log_message("⚠️ Processamento finalizado com alguns problemas")
                messagebox.showwarning("Aviso", "Processamento finalizado. Verifique os logs e resultados.")
            
            # Limpar arquivo temporário
            try:
                os.remove(script_filename)
            except:
                pass
                
        except Exception as e:
            logging.error(f"Erro: {e}")
            logging.error(traceback.format_exc())
            self.status_label.config(text="❌ Erro")
            self.log_message(f"❌ Erro: {str(e)}")
            messagebox.showerror("Erro", f"Erro: {str(e)}")


if __name__ == "__main__":
    root = tk.Tk()
    app = Application(master=root)
    app.mainloop()
