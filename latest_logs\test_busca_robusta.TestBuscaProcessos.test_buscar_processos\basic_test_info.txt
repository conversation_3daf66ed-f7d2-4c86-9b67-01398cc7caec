test_busca_robusta.py::TestBuscaProcessos::test_buscar_processos
--------------------------------------------------------------------
Last Page: chrome://new-tab-page/
 Duration: 0.09s
  Browser: Chrome 137.0.7151.57
   Driver: chromedriver 137.0.7151.68
Timestamp: 1749037221  (Unix Timestamp)
     Date: Wednesday, 4 June 2025
     Time: 8:40:21 AM  (Hora oficial do Brasil, UTC-03:00)
--------------------------------------------------------------------
Traceback:
  File "C:\Users\<USER>\Desktop\buscaNomeCPF\test_busca_robusta.py", line 13, in test_buscar_processos
    print("🚀 Iniciando SeleniumBase UC Mode...")
  File "C:\Users\<USER>\Desktop\buscaNomeCPF\.venv\Lib\site-packages\colorama\ansitowin32.py", line 47, in write
    self.__convertor.write(text)
  File "C:\Users\<USER>\Desktop\buscaNomeCPF\.venv\Lib\site-packages\colorama\ansitowin32.py", line 177, in write
    self.write_and_convert(text)
  File "C:\Users\<USER>\Desktop\buscaNomeCPF\.venv\Lib\site-packages\colorama\ansitowin32.py", line 205, in write_and_convert
    self.write_plain_text(text, cursor, len(text))
  File "C:\Users\<USER>\Desktop\buscaNomeCPF\.venv\Lib\site-packages\colorama\ansitowin32.py", line 210, in write_plain_text
    self.wrapped.write(text[start:end])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>