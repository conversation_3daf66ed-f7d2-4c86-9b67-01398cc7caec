#!/usr/bin/env python3
"""
Script para criar uma planilha de exemplo com números de processo
"""

import pandas as pd
import os

def criar_planilha_exemplo():
    """Cria uma planilha de exemplo com números de processo"""
    
    # Dados de exemplo (substitua pelos seus números de processo reais)
    dados_exemplo = {
        'Processos': [  # A primeira coluna será usada automaticamente
            '1234567-89.2023.8.09.0001',
            '2345678-90.2023.8.09.0002', 
            '3456789-01.2023.8.09.0003',
            '4567890-12.2023.8.09.0004',
            '5678901-23.2023.8.09.0005'
        ],
        'Observações': [  # Colunas adicionais são opcionais
            'Processo A',
            'Processo B', 
            'Processo C',
            'Processo D',
            'Processo E'
        ]
    }
    
    # Criar DataFrame
    df = pd.DataFrame(dados_exemplo)
    
    # Salvar planilha
    nome_arquivo = 'exemplo_processos.xlsx'
    df.to_excel(nome_arquivo, index=False)
    
    print(f"✅ Planilha de exemplo criada: {nome_arquivo}")
    print(f"📁 Local: {os.path.abspath(nome_arquivo)}")
    print("\n📋 Formato da planilha:")
    print("- Primeira coluna: Números de processo (obrigatório)")
    print("- Outras colunas: Opcionais (serão preservadas)")
    print("\n🔧 O programa irá:")
    print("1. Usar automaticamente a primeira coluna como números de processo")
    print("2. Adicionar colunas: 'Nome Polo Ativo', 'CPF Polo Ativo', 'Processo Encontrado'")
    print("3. Salvar resultados em: Resultados_YYYYMMDD.xlsx")
    
    return nome_arquivo

if __name__ == "__main__":
    print("🔧 CRIADOR DE PLANILHA DE EXEMPLO")
    print("=" * 50)
    
    try:
        arquivo = criar_planilha_exemplo()
        
        print("\n" + "=" * 50)
        print("✅ PLANILHA CRIADA COM SUCESSO!")
        print("\nPara usar:")
        print("1. Edite o arquivo criado com seus números de processo")
        print("2. Execute: python buscaNomeCPF.py")
        print("3. Selecione a planilha criada")
        print("4. Inicie a busca")
        
    except Exception as e:
        print(f"❌ Erro ao criar planilha: {e}")
