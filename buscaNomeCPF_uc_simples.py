import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import threading
from datetime import datetime
import logging
import traceback
import os

# Configuração do logging
logging.basicConfig(filename='busca_nome_cpf.log', level=logging.DEBUG,
                    format='%(asctime)s - %(levelname)s - %(message)s')

# Importar SeleniumBase
try:
    from seleniumbase import BaseCase
    SELENIUMBASE_AVAILABLE = True
    logging.info("SeleniumBase disponível")
except ImportError:
    SELENIUMBASE_AVAILABLE = False
    logging.error("SeleniumBase não disponível")


class BuscaProcessos(BaseCase):
    """Classe que herda do BaseCase para usar UC Mode"""
    
    def setUp(self):
        """Configuração inicial do UC Mode"""
        super().setUp()
        self.set_window_size(1200, 800)
        
    def fazer_login(self, usuario, senha):
        """Faz login no sistema"""
        logging.info("Iniciando login...")
        
        # Abrir página de login usando UC Mode
        self.uc_open_with_reconnect("https://projudi.tjgo.jus.br/LogOn?PaginaAtual=-200")
        self.sleep(3)
        
        # Inserir credenciais
        self.uc_type("#login", usuario)
        self.sleep(1)
        self.uc_type("#senha", senha)
        self.sleep(1)
        
        # Clicar no botão de login
        self.uc_click("input[type='submit']")
        self.sleep(5)
        
        # Verificar se login foi bem-sucedido
        if self.is_element_present("#menuPrinciapl"):
            logging.info("Login realizado com sucesso!")
            return True
        else:
            logging.error("Falha no login")
            return False
    
    def buscar_processo(self, numero_processo):
        """Busca um processo específico"""
        resultados = {'Processo Encontrado': 'Não'}
        
        try:
            logging.info(f"Buscando processo: {numero_processo}")
            
            # Navegar para busca
            self.uc_open_with_reconnect("https://projudi.tjgo.jus.br/BuscaProcesso?PaginaAtual=4&TipoConsultaProcesso=24")
            self.sleep(2)
            
            # Inserir número do processo
            self.uc_type("#ProcessoNumero", numero_processo)
            self.sleep(1)
            
            # Clicar em mostrar todos os processos
            try:
                self.uc_click("button[onclick*='MostrarTodos']")
                self.sleep(1)
            except:
                pass
            
            # Clicar em pesquisar
            self.uc_click("#divBotoesCentralizados input[type='submit']")
            self.sleep(3)
            
            # Verificar se processo foi encontrado
            if self.is_text_visible("Polo Ativo") or self.is_element_present("div[id*='resultado']"):
                resultados['Processo Encontrado'] = 'Sim'
                logging.info(f"Processo {numero_processo} encontrado!")
                
                # Extrair dados
                try:
                    # Buscar nome do polo ativo
                    if self.is_element_present("span:contains('Nome')"):
                        nome = self.get_text("span:contains('Nome')")
                        resultados['Nome Polo Ativo'] = nome.strip()
                except:
                    resultados['Nome Polo Ativo'] = ''
                
                try:
                    # Buscar CPF do polo ativo
                    if self.is_element_present("span:contains('CPF')"):
                        cpf = self.get_text("span:contains('CPF')")
                        resultados['CPF Polo Ativo'] = cpf.strip()
                except:
                    resultados['CPF Polo Ativo'] = ''
            else:
                logging.warning(f"Processo {numero_processo} não encontrado")
                
            return resultados
            
        except Exception as e:
            logging.error(f"Erro ao buscar processo {numero_processo}: {e}")
            return resultados


class Application(tk.Frame):
    def __init__(self, master=None):
        super().__init__(master)
        self.master = master
        self.master.title("Busca Nome e CPF - UC Mode Simples")
        self.master.geometry("800x600")
        self.master.configure(bg="#2c3e50")
        self.excel_dir = None
        self.porcentagem_concluida = 0
        self.processo_num = "Inicializando..."
        self.last_result = "Buscando..."
        self.salvando = ""
        self.processos_nao_encontrados = 0
        self.total_processos = 0
        self.not_found_logs = []
        self.create_widgets()

    def create_widgets(self):
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TFrame', background="#2c3e50")
        style.configure('TButton', font=('Arial', 10, 'bold'), borderwidth=1,
                        background="#3498db", foreground="white")
        style.configure('TLabel', font=('Arial', 11),
                        background="#2c3e50", foreground="white")
        style.configure('TEntry', font=('Arial', 10),
                        fieldbackground="#34495e", foreground="white")

        main_frame = ttk.Frame(self.master, padding="30 30 30 30", style='TFrame')
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        main_frame.columnconfigure(0, weight=1)
        self.master.columnconfigure(0, weight=1)
        self.master.rowconfigure(0, weight=1)

        ttk.Label(main_frame, text="Busca Nome e CPF - UC Mode", font=(
            'Arial', 16, 'bold')).grid(column=0, row=0, columnspan=2, pady=20)

        ttk.Label(main_frame, text="Usuário:").grid(column=0, row=1, sticky=tk.W, pady=5)
        self.entrada_usuario = ttk.Entry(main_frame)
        self.entrada_usuario.grid(column=0, row=2, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(main_frame, text="Senha:").grid(column=0, row=3, sticky=tk.W, pady=5)
        self.entrada_senha = ttk.Entry(main_frame, show="*")
        self.entrada_senha.grid(column=0, row=4, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(main_frame, text="Selecione a planilha Excel (primeira coluna = números de processo):").grid(
            column=0, row=5, sticky=tk.W, pady=10)
        self.excel_dir_entry = ttk.Entry(main_frame, width=50)
        self.excel_dir_entry.grid(column=0, row=6, sticky=(tk.W, tk.E), pady=5)
        ttk.Button(main_frame, text="Selecionar Arquivo",
                   command=self.select_file).grid(column=1, row=6, sticky=tk.W, padx=5, pady=5)

        ttk.Button(main_frame, text="Iniciar Busca", command=self.start_progress).grid(
            column=0, row=7, sticky=(tk.W, tk.E), pady=20, columnspan=2)

        # Status
        self.status_label = ttk.Label(main_frame, text="Pronto para iniciar", font=('Arial', 10))
        self.status_label.grid(column=0, row=8, columnspan=2, pady=10)

    def select_file(self):
        file_path = filedialog.askopenfilename(filetypes=[("Excel files", "*.xlsx *.xls")])
        self.excel_dir_entry.delete(0, tk.END)
        self.excel_dir_entry.insert(0, file_path)

    def start_progress(self):
        if not SELENIUMBASE_AVAILABLE:
            messagebox.showerror("Erro", "SeleniumBase não está instalado. Execute: pip install seleniumbase")
            return
            
        self.excel_dir = self.excel_dir_entry.get()
        usuario = self.entrada_usuario.get()
        senha = self.entrada_senha.get()

        if not self.excel_dir:
            messagebox.showerror("Erro", "Por favor, selecione um arquivo Excel.")
            return
        if not usuario or not senha:
            messagebox.showerror("Erro", "Por favor, preencha o usuário e senha.")
            return

        self.status_label.config(text="Iniciando SeleniumBase UC Mode...")
        t = threading.Thread(target=self.processar_planilha, args=(usuario, senha))
        t.start()

    def processar_planilha(self, usuario, senha):
        """Processa a planilha usando UC Mode"""
        try:
            # Carregar planilha
            df = pd.read_excel(self.excel_dir)
            
            if df.empty or len(df.columns) == 0:
                messagebox.showerror("Erro", "A planilha está vazia ou não possui colunas.")
                return
            
            primeira_coluna = df.columns[0]
            df = df.rename(columns={primeira_coluna: 'Número do Processo'})
            df = df.dropna(subset=['Número do Processo'])
            
            if len(df) == 0:
                messagebox.showerror("Erro", "Não há números de processo válidos na primeira coluna.")
                return

            novas_colunas = ['Nome Polo Ativo', 'CPF Polo Ativo', 'Processo Encontrado']
            for coluna in novas_colunas:
                if coluna not in df.columns:
                    df[coluna] = ''
            
            logging.info(f"Planilha carregada: {len(df)} processos")

            # Iniciar SeleniumBase UC Mode
            self.status_label.config(text="Abrindo Chrome com UC Mode...")
            
            # Usar pytest para executar o UC Mode
            import pytest
            import sys
            
            # Criar arquivo temporário de teste
            test_content = f'''
import pytest
from seleniumbase import BaseCase

class TestBusca(BaseCase):
    def test_busca_processos(self):
        # Login
        self.uc_open_with_reconnect("https://projudi.tjgo.jus.br/LogOn?PaginaAtual=-200")
        self.sleep(3)
        self.uc_type("#login", "{usuario}")
        self.sleep(1)
        self.uc_type("#senha", "{senha}")
        self.sleep(1)
        self.uc_click("input[type='submit']")
        self.sleep(5)
        
        # Verificar login
        assert self.is_element_present("#menuPrinciapl"), "Login falhou"
        
        # Processar cada processo
        processos = {list(df['Número do Processo'].values)}
        
        for processo in processos:
            print(f"Processando: {{processo}}")
            self.uc_open_with_reconnect("https://projudi.tjgo.jus.br/BuscaProcesso?PaginaAtual=4&TipoConsultaProcesso=24")
            self.sleep(2)
            self.uc_type("#ProcessoNumero", str(processo))
            self.sleep(1)
            try:
                self.uc_click("button[onclick*='MostrarTodos']")
                self.sleep(1)
            except:
                pass
            self.uc_click("#divBotoesCentralizados input[type='submit']")
            self.sleep(3)
'''
            
            with open('test_temp.py', 'w', encoding='utf-8') as f:
                f.write(test_content)
            
            # Executar teste com UC Mode
            self.status_label.config(text="Executando UC Mode...")
            result = pytest.main(['-v', '--uc', 'test_temp.py'])
            
            # Limpar arquivo temporário
            if os.path.exists('test_temp.py'):
                os.remove('test_temp.py')
            
            if result == 0:
                messagebox.showinfo("Sucesso", "Processamento concluído!")
            else:
                messagebox.showerror("Erro", "Erro durante o processamento")
                
        except Exception as e:
            logging.error(f"Erro: {e}")
            messagebox.showerror("Erro", f"Erro: {str(e)}")
        finally:
            self.status_label.config(text="Pronto para iniciar")


if __name__ == "__main__":
    root = tk.Tk()
    app = Application(master=root)
    app.mainloop()
