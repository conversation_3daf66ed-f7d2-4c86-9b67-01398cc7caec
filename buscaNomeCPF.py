import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import pandas as pd
import threading
from datetime import datetime
import logging
import traceback
import os
import chromedriver_autoinstaller
# Tentar importar undetected_chromedriver, usar fallback se falhar
try:
    import undetected_chromedriver as uc
    UC_AVAILABLE = True
except ImportError:
    UC_AVAILABLE = False
    logging.warning("undetected_chromedriver não disponível, usando driver padrão")

# Tentar importar selenium_stealth, usar fallback se falhar
try:
    from selenium_stealth import stealth
    STEALTH_AVAILABLE = True
except ImportError:
    STEALTH_AVAILABLE = False
    logging.warning("selenium_stealth não disponível, usando configurações básicas")
import random
import time

# Importar configurações anti-detecção
try:
    from config_anti_deteccao import *
except ImportError:
    # Configurações padrão se o arquivo de config não existir
    ALL_USER_AGENTS = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
    ]
    ALL_CHROME_FLAGS = ["--start-maximized", "--disable-extensions", "--no-sandbox"]
    STEALTH_CONFIG = {"languages": ["pt-BR", "pt"], "vendor": "Google Inc.", "platform": "Win32"}
    WEBDRIVER_TIMEOUT = 15
    DELAY_ENTRE_PROCESSOS_MIN, DELAY_ENTRE_PROCESSOS_MAX = 5, 10
    DELAY_ACAO_MIN, DELAY_ACAO_MAX = 1, 3
    DELAY_CARREGAMENTO_MIN, DELAY_CARREGAMENTO_MAX = 3, 6
    DELAY_DIGITACAO_MIN, DELAY_DIGITACAO_MAX = 0.05, 0.15

# Configuração do logging
logging.basicConfig(filename='busca_nome_cpf.log', level=logging.DEBUG,
                    format='%(asctime)s - %(levelname)s - %(message)s')

def get_random_user_agent():
    """Retorna um User-Agent aleatório da lista"""
    return random.choice(ALL_USER_AGENTS)

def human_delay(min_seconds=1, max_seconds=3):
    """Implementa delay aleatório para simular comportamento humano"""
    delay = random.uniform(min_seconds, max_seconds)
    time.sleep(delay)

def create_stealth_driver():
    """Cria um driver Chrome com configurações anti-detecção"""
    try:
        if UC_AVAILABLE:
            # Usar undetected-chromedriver se disponível
            logging.info("Usando undetected-chromedriver...")
            options = uc.ChromeOptions()

            # Adicionar todas as flags de configuração
            for flag in ALL_CHROME_FLAGS:
                options.add_argument(flag)

            # User-Agent aleatório
            user_agent = get_random_user_agent()
            options.add_argument(f"--user-agent={user_agent}")
            logging.info(f"Usando User-Agent: {user_agent}")

            # Criar driver com undetected-chromedriver
            driver = uc.Chrome(options=options, version_main=None)

            # Aplicar selenium-stealth se disponível
            if STEALTH_AVAILABLE:
                logging.info("Aplicando configurações stealth...")
                stealth(driver, **STEALTH_CONFIG)

        else:
            # Usar driver padrão com configurações anti-detecção
            logging.info("Usando driver Chrome padrão com configurações anti-detecção...")
            options = Options()

            # Adicionar flags básicas
            basic_flags = [
                "--start-maximized",
                "--disable-blink-features=AutomationControlled",
                "--disable-extensions",
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-gpu",
                "--disable-software-rasterizer",
                "--ignore-certificate-errors",
                "--allow-running-insecure-content",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor"
            ]

            for flag in basic_flags:
                options.add_argument(flag)

            # User-Agent aleatório
            user_agent = get_random_user_agent()
            options.add_argument(f"--user-agent={user_agent}")
            logging.info(f"Usando User-Agent: {user_agent}")

            # Configurações experimentais
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)

            service = Service(chromedriver_autoinstaller.install())
            driver = webdriver.Chrome(service=service, options=options)

        # Configurar timeouts
        driver.set_page_load_timeout(PAGE_LOAD_TIMEOUT if 'PAGE_LOAD_TIMEOUT' in globals() else 30)
        driver.set_script_timeout(SCRIPT_TIMEOUT if 'SCRIPT_TIMEOUT' in globals() else 30)

        # Configurações adicionais via CDP
        try:
            logging.info("Aplicando configurações CDP...")
            cdp_script = CDP_SCRIPT if 'CDP_SCRIPT' in globals() else '''
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                window.chrome = { runtime: {} };
            '''

            driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': cdp_script
            })
        except Exception as e:
            logging.warning(f"Não foi possível aplicar configurações CDP: {e}")

        logging.info("Driver criado com sucesso!")
        return driver

    except Exception as e:
        logging.error(f"Erro ao criar driver: {str(e)}")
        logging.error(traceback.format_exc())
        # Fallback para driver normal se houver erro
        logging.info("Tentando fallback para driver normal...")
        return create_fallback_driver()

def create_fallback_driver():
    """Cria um driver Chrome normal como fallback"""
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-software-rasterizer")
    chrome_options.add_argument('--ignore-certificate-errors')
    chrome_options.add_argument('--allow-running-insecure-content')

    service = Service(chromedriver_autoinstaller.install())
    return webdriver.Chrome(service=service, options=chrome_options)


class Application(tk.Frame):
    def __init__(self, master=None):
        super().__init__(master)
        self.master = master
        self.master.title("Busca Nome e CPF")
        self.master.geometry("800x700")
        self.master.configure(bg="#2c3e50")
        self.excel_dir = None
        self.porcentagem_concluida = 0
        self.processo_num = "Inicializando..."
        self.last_result = "Buscando..."
        self.salvando = ""
        self.processos_nao_encontrados = 0
        self.total_processos = 0
        self.not_found_logs = []
        self.create_widgets()

    def create_widgets(self):
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TFrame', background="#2c3e50")
        style.configure('TButton', font=('Arial', 10, 'bold'), borderwidth=1,
                        background="#3498db", foreground="white")
        style.configure('TLabel', font=('Arial', 11),
                        background="#2c3e50", foreground="white")
        style.configure('TEntry', font=('Arial', 10),
                        fieldbackground="#34495e", foreground="white")

        main_frame = ttk.Frame(
            self.master, padding="30 30 30 30", style='TFrame')
        main_frame.grid(row=0, column=0, sticky=(
            tk.W, tk.E, tk.N, tk.S))
        main_frame.columnconfigure(0, weight=1)
        self.master.columnconfigure(0, weight=1)
        self.master.rowconfigure(0, weight=1)

        ttk.Label(main_frame, text="Busca Nome e CPF", font=(
            'Arial', 16, 'bold')).grid(column=0, row=0, columnspan=2, pady=20)

        ttk.Label(main_frame, text="Usuário:").grid(
            column=0, row=1, sticky=tk.W, pady=5)
        self.entrada_usuario = ttk.Entry(main_frame)
        self.entrada_usuario.grid(
            column=0, row=2, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(main_frame, text="Senha:").grid(
            column=0, row=3, sticky=tk.W, pady=5)
        self.entrada_senha = ttk.Entry(main_frame, show="*")
        self.entrada_senha.grid(
            column=0, row=4, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(main_frame, text="Selecione a planilha Excel (primeira coluna = números de processo):").grid(
            column=0, row=5, sticky=tk.W, pady=10)
        self.excel_dir_entry = ttk.Entry(main_frame, width=50)
        self.excel_dir_entry.grid(
            column=0, row=6, sticky=(tk.W, tk.E), pady=5)
        ttk.Button(main_frame, text="Selecionar Arquivo",
                   command=self.select_file).grid(column=1, row=6, sticky=tk.W, padx=5, pady=5)

        ttk.Button(main_frame, text="Iniciar Busca", command=self.start_progress).grid(
            column=0, row=7, sticky=(tk.W, tk.E), pady=20, columnspan=2)

    def select_file(self):
        file_path = filedialog.askopenfilename(
            filetypes=[("Excel files", "*.xlsx *.xls")])
        self.excel_dir_entry.delete(0, tk.END)
        self.excel_dir_entry.insert(0, file_path)

    def start_progress(self):
        self.excel_dir = self.excel_dir_entry.get()
        usuario = self.entrada_usuario.get()
        senha = self.entrada_senha.get()

        if not self.excel_dir:
            messagebox.showerror(
                "Erro", "Por favor, selecione um arquivo Excel.")
            return
        if not usuario or not senha:
            messagebox.showerror(
                "Erro", "Por favor, preencha o usuário e senha.")
            return

        t = threading.Thread(target=self.processar_planilha,
                             args=(usuario, senha))
        t.start()
        self.show_progress_window()

    def show_progress_window(self):
        self.progress_window = tk.Toplevel(self.master)
        self.progress_window.title("Progresso atual")
        self.progress_window.geometry("500x500")
        self.progress_window.configure(bg="#2c3e50")

        frame = ttk.Frame(self.progress_window, padding="30", style='TFrame')
        frame.grid(row=0, column=0, sticky=(
            tk.W, tk.E, tk.N, tk.S))
        frame.columnconfigure(0, weight=1)
        self.progress_window.columnconfigure(0, weight=1)
        self.progress_window.rowconfigure(0, weight=1)

        ttk.Label(frame, text="Progresso da Busca", font=(
            'Arial', 14, 'bold')).grid(column=0, row=0, pady=20)

        self.progress_bar = ttk.Progressbar(
            frame, orient="horizontal", length=400, mode="determinate", style="TProgressbar")
        self.progress_bar.grid(column=0, row=1, pady=20)

        self.progress_text = ttk.Label(frame, text="0%", font=('Arial', 12))
        self.progress_text.grid(column=0, row=2, pady=10)

        self.processo_text = ttk.Label(
            frame, text=f"Processo: {self.processo_num}", font=('Arial', 11))
        self.processo_text.grid(column=0, row=3, pady=10)

        self.last_result_text = ttk.Label(
            frame, text=f"Resultado: {self.last_result}", font=('Arial', 11))
        self.last_result_text.grid(column=0, row=4, pady=10)

        self.salvando_text = ttk.Label(frame, text="", font=('Arial', 10))
        self.salvando_text.grid(column=0, row=5, pady=10)

        self.processos_nao_encontrados_text = ttk.Label(
            frame, text="Processos não encontrados: 0", font=('Arial', 11))
        self.processos_nao_encontrados_text.grid(column=0, row=6, pady=10)

        self.total_processos_text = ttk.Label(
            frame, text="Total de processos: 0", font=('Arial', 11))
        self.total_processos_text.grid(column=0, row=7, pady=10)

        ttk.Label(frame, text="Logs:", font=('Arial', 12, 'bold')).grid(
            column=0, row=8, pady=10)
        self.log_text = tk.Text(frame, height=10, width=60)
        self.log_text.grid(column=0, row=9, pady=5)

        self.update_progress()

    def update_progress(self):
        self.progress_bar["value"] = self.porcentagem_concluida * 100
        self.progress_text["text"] = f"{round(self.porcentagem_concluida * 100, 2)}%"
        self.processo_text["text"] = f"Processo: {self.processo_num}"
        self.last_result_text["text"] = f"Resultado: {self.last_result}"
        self.salvando_text["text"] = self.salvando
        self.processos_nao_encontrados_text["text"] = f"Processos não encontrados: {self.processos_nao_encontrados}"
        self.total_processos_text["text"] = f"Total de processos: {self.total_processos}"

        if self.not_found_logs:
            self.log_text.delete(1.0, tk.END)
            for log in self.not_found_logs[-10:]:
                self.log_text.insert(tk.END, log + '\n')

        if self.porcentagem_concluida < 1:
            self.master.after(200, self.update_progress)
        else:
            ttk.Button(self.progress_window, text="Fechar", command=self.progress_window.destroy).grid(
                column=0, row=10, pady=20)

    def extrair_dados_processo(self, driver, wait, numero_processo):
        resultados = {'Processo Encontrado': 'Não'}

        try:
            # Delay antes de navegar
            human_delay(DELAY_ACAO_MIN, DELAY_ACAO_MAX)

            # Navegar até a página de busca de processos
            driver.get(
                "https://projudi.tjgo.jus.br/BuscaProcesso?PaginaAtual=4&TipoConsultaProcesso=24")

            # Aguardar carregamento da página
            human_delay(DELAY_CARREGAMENTO_MIN, DELAY_CARREGAMENTO_MAX)

            # Inserir o número do processo com simulação de digitação humana
            processo_input = wait.until(
                EC.presence_of_element_located((By.XPATH, '//*[@id="ProcessoNumero"]')))
            processo_input.clear()

            # Simular digitação humana
            for char in numero_processo:
                processo_input.send_keys(char)
                time.sleep(random.uniform(DELAY_DIGITACAO_MIN, DELAY_DIGITACAO_MAX))  # Delay entre caracteres

            # Delay antes de clicar no botão
            human_delay(1, 2)

            # Clicar no botão para mostrar todos os processos (remover filtro de ativos)
            try:
                mostrar_todos_button = wait.until(EC.element_to_be_clickable(
                    (By.XPATH, '/html/body/div/form/div/fieldset/div[5]/fieldset/div[1]/label/button[2]')))
                mostrar_todos_button.click()
                human_delay(1, 2)  # Delay após clicar
            except Exception as e:
                message = f"Processo {numero_processo}: Botão para mostrar todos os processos não encontrado."
                logging.warning(message)
                self.not_found_logs.append(message)

            # Delay antes de pesquisar
            human_delay(1, 3)

            # Clicar no botão "Pesquisar"
            pesquisar_button = wait.until(EC.element_to_be_clickable(
                (By.XPATH, '//*[@id="divBotoesCentralizados"]/input[1]')))
            pesquisar_button.click()

            # Delay após pesquisar para aguardar carregamento
            human_delay(3, 6)

            # Verificar se o processo foi encontrado
            try:
                wait.until(EC.presence_of_element_located(
                    (By.XPATH, '/html/body/div[2]/form')))
                resultados['Processo Encontrado'] = 'Sim'
            except TimeoutException:
                message = f"Processo {numero_processo} não encontrado."
                logging.warning(message)
                self.not_found_logs.append(message)
                resultados['Processo Encontrado'] = 'Não'
                return resultados

            # Extrair informações do polo ativo
            try:
                nome_polo_ativo = driver.find_element(
                    By.XPATH, '/html/body/div[2]/form/div[1]/fieldset/fieldset[2]/fieldset[1]/fieldset/span[1]').text.strip()
                resultados['Nome Polo Ativo'] = nome_polo_ativo
            except Exception as e:
                message = f"Processo {numero_processo}: Nome Polo Ativo não encontrado."
                logging.warning(message)
                self.not_found_logs.append(message)
                resultados['Nome Polo Ativo'] = ''

            try:
                cpf_polo_ativo = driver.find_element(
                    By.XPATH, '/html/body/div[2]/form/div[1]/fieldset/fieldset[2]/fieldset[1]/fieldset/span[2]').text.strip()
                resultados['CPF Polo Ativo'] = cpf_polo_ativo
            except Exception as e:
                message = f"Processo {numero_processo}: CPF Polo Ativo não encontrado."
                logging.warning(message)
                self.not_found_logs.append(message)
                resultados['CPF Polo Ativo'] = ''

            return resultados

        except Exception as e:
            message = f"Erro ao processar processo {numero_processo}: {str(e)}"
            logging.error(message)
            logging.error(traceback.format_exc())
            self.not_found_logs.append(message)
            self.capture_screenshot(driver, f"erro_processo_{numero_processo}")
            return resultados

    def capture_screenshot(self, driver, filename):
        try:
            if not os.path.exists('screenshots'):
                os.makedirs('screenshots')
            driver.save_screenshot(f"screenshots/{filename}.png")
            logging.info(f"Screenshot salvo: {filename}.png")
        except Exception as e:
            logging.error(f"Erro ao capturar screenshot: {str(e)}")

    def processar_planilha(self, usuario, senha):
        try:
            df = pd.read_excel(self.excel_dir)

            # Verificar se a planilha tem pelo menos uma coluna
            if df.empty or len(df.columns) == 0:
                messagebox.showerror(
                    "Erro", "A planilha está vazia ou não possui colunas.")
                return

            # Usar a primeira coluna como números de processo
            primeira_coluna = df.columns[0]
            logging.info(f"Usando a primeira coluna '{primeira_coluna}' como números de processo")

            # Renomear a primeira coluna para padronizar
            df = df.rename(columns={primeira_coluna: 'Número do Processo'})

            # Verificar se há dados na primeira coluna
            if df['Número do Processo'].isna().all():
                messagebox.showerror(
                    "Erro", "A primeira coluna não contém dados válidos.")
                return

            # Remover linhas vazias na primeira coluna
            df = df.dropna(subset=['Número do Processo'])

            if len(df) == 0:
                messagebox.showerror(
                    "Erro", "Não há números de processo válidos na primeira coluna.")
                return

            novas_colunas = ['Nome Polo Ativo', 'CPF Polo Ativo', 'Processo Encontrado']
            for coluna in novas_colunas:
                if coluna not in df.columns:
                    df[coluna] = ''

            logging.info(f"Planilha carregada: {len(df)} processos encontrados na primeira coluna")

            # Usar o driver stealth anti-detecção
            logging.info("Criando driver com configurações anti-detecção...")
            driver = create_stealth_driver()
            wait = WebDriverWait(driver, WEBDRIVER_TIMEOUT)  # Timeout configurável

            try:
                # Login com delays humanos
                logging.info("Iniciando processo de login...")
                driver.get(
                    "https://projudi.tjgo.jus.br/LogOn?PaginaAtual=-200")

                # Aguardar carregamento da página de login
                human_delay(DELAY_CARREGAMENTO_MIN, DELAY_CARREGAMENTO_MAX)

                # Inserir usuário com simulação de digitação humana
                login_input = wait.until(EC.presence_of_element_located(
                    (By.XPATH, '//*[@id="login"]')))
                login_input.clear()
                for char in usuario:
                    login_input.send_keys(char)
                    time.sleep(random.uniform(DELAY_DIGITACAO_MIN, DELAY_DIGITACAO_MAX))

                human_delay(DELAY_ACAO_MIN, DELAY_ACAO_MAX)

                # Inserir senha com simulação de digitação humana
                senha_input = driver.find_element(By.XPATH, '//*[@id="senha"]')
                senha_input.clear()
                for char in senha:
                    senha_input.send_keys(char)
                    time.sleep(random.uniform(DELAY_DIGITACAO_MIN, DELAY_DIGITACAO_MAX))

                human_delay(DELAY_ACAO_MIN, DELAY_ACAO_MAX)

                # Clicar no botão de login
                login_button = driver.find_element(
                    By.XPATH, '//*[@id="formLogin"]/div[4]/input[1]')
                login_button.click()

                # Aguardar carregamento após login
                human_delay(DELAY_CARREGAMENTO_MIN, DELAY_CARREGAMENTO_MAX)

                # Verificar se o login foi bem-sucedido
                logging.info("Verificando se o login foi bem-sucedido...")
                wait.until(EC.presence_of_element_located(
                    (By.XPATH, '//*[@id="menuPrinciapl"]/ul[2]/li')))
                logging.info("Login realizado com sucesso!")

                total_registros = len(df)
                self.total_processos = total_registros
                logging.info(f"Iniciando processamento de {total_registros} processos...")

                for index, row in df.iterrows():
                    processo_num = str(row['Número do Processo'])
                    self.processo_num = processo_num
                    logging.info(f"Processando processo {index + 1}/{total_registros}: {processo_num}")

                    resultados = self.extrair_dados_processo(
                        driver, wait, processo_num)

                    df.at[index, 'Processo Encontrado'] = resultados.get(
                        'Processo Encontrado', 'Não')
                    if resultados.get('Processo Encontrado') == 'Sim':
                        df.at[index, 'Nome Polo Ativo'] = resultados.get(
                            'Nome Polo Ativo', '')
                        df.at[index, 'CPF Polo Ativo'] = resultados.get(
                            'CPF Polo Ativo', '')
                        logging.info(f"Processo {processo_num} encontrado com sucesso")
                    else:
                        self.processos_nao_encontrados += 1
                        logging.warning(f"Processo {processo_num} não encontrado")

                    # Atualizar progresso
                    self.porcentagem_concluida = (index + 1) / total_registros
                    self.last_result = f"Processo {processo_num} {'encontrado' if resultados.get('Processo Encontrado') == 'Sim' else 'não encontrado'}"

                    # Delay entre processos para evitar detecção
                    if index < total_registros - 1:  # Não fazer delay no último processo
                        delay_entre_processos = random.uniform(DELAY_ENTRE_PROCESSOS_MIN, DELAY_ENTRE_PROCESSOS_MAX)
                        logging.info(f"Aguardando {delay_entre_processos:.1f}s antes do próximo processo...")
                        time.sleep(delay_entre_processos)

                    # Salvar a cada 50 registros processados
                    if (index + 1) % 50 == 0:
                        novo_nome_arquivo = f"Resultados_{datetime.now().strftime('%Y%m%d')}.xlsx"
                        script_dir = os.path.dirname(os.path.abspath(__file__))
                        output_path = os.path.join(script_dir, novo_nome_arquivo)
                        df.to_excel(output_path, index=False)
                        self.salvando = f"Salvando... Última vez salvo: {datetime.now().strftime('%H:%M')}"
                        print(
                            f"Salvando... Última vez salvo: {datetime.now().strftime('%H:%M')}")

                # Salvar a planilha final no mesmo local do script
                novo_nome_arquivo = f"Resultados_{datetime.now().strftime('%Y%m%d')}.xlsx"
                script_dir = os.path.dirname(os.path.abspath(__file__))
                output_path = os.path.join(script_dir, novo_nome_arquivo)
                df.to_excel(output_path, index=False)
                messagebox.showinfo(
                    "Concluído", f"Processamento da planilha concluído com sucesso!\nResultados salvos em {output_path}")

            except Exception as e:
                logging.error(f"Erro durante o processamento: {str(e)}")
                logging.error(traceback.format_exc())
                messagebox.showerror(
                    "Erro", f"Ocorreu um erro durante o processamento: {str(e)}")
            finally:
                driver.quit()
                # Salvar a planilha mesmo em caso de erro
                novo_nome_arquivo = f"Resultados_{datetime.now().strftime('%Y%m%d')}_parcial.xlsx"
                script_dir = os.path.dirname(os.path.abspath(__file__))
                output_path = os.path.join(script_dir, novo_nome_arquivo)
                df.to_excel(output_path, index=False)
                logging.info(
                    f"Planilha salva parcialmente em {output_path}")

        except Exception as e:
            logging.error(f"Erro ao processar a planilha: {str(e)}")
            logging.error(traceback.format_exc())
            messagebox.showerror(
                "Erro", f"Ocorreu um erro ao processar a planilha: {str(e)}")


if __name__ == "__main__":
    root = tk.Tk()
    app = Application(master=root)
    app.mainloop()